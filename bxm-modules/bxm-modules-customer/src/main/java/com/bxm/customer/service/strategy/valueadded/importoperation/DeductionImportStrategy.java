package com.bxm.customer.service.strategy.valueadded.importoperation;

import com.bxm.customer.domain.ValueAddedDeliveryOrder;
import com.bxm.customer.domain.ValueAddedFile;
import com.bxm.customer.domain.dto.StatusChangeRequestDTO;
import com.bxm.customer.domain.dto.valueAdded.BatchImportOperationRequest;
import com.bxm.customer.domain.dto.valueAdded.BatchImportOperationResult;
import com.bxm.customer.domain.dto.valueAdded.BatchOperationErrorDTO;
import com.bxm.customer.domain.dto.valueAdded.BaseImportExcelDTO;
import com.bxm.customer.domain.dto.valueAdded.DeductionImportExcelDTO;
import com.bxm.customer.domain.dto.valueAdded.ImportValidationResult;
import com.bxm.customer.domain.dto.valueAdded.TemplateParseResult;
import com.bxm.customer.helper.ValueAddedImportValidationHelper;
import com.bxm.customer.domain.enums.ValueAddedDeliveryOrderStatus;
import com.bxm.customer.domain.enums.ValueAddedBatchImportOperationType;
import com.bxm.customer.service.IValueAddedDeliveryOrderService;
import com.bxm.customer.service.IValueAddedFileService;
import com.bxm.customer.service.strategy.ImportOperationStrategy;
import com.bxm.common.core.utils.poi.ExcelUtil;
import com.bxm.common.security.utils.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 扣款导入策略实现
 *
 * 处理扣款操作的导入逻辑：
 * 1. 验证交付单状态必须为"已确认待扣款"
 * 2. 修改状态为"已扣款"
 * 3. 保存相关附件文件到value_added_file表
 *
 * <AUTHOR>
 * @date 2025-08-25
 */
@Slf4j
@Component
public class DeductionImportStrategy implements ImportOperationStrategy {

    @Autowired
    private IValueAddedDeliveryOrderService deliveryOrderService;

    @Autowired
    private IValueAddedFileService fileService;

    @Autowired
    private ValueAddedImportValidationHelper valueAddedImportValidationHelper;

    @Override
    public ValueAddedBatchImportOperationType getSupportedOperationType() {
        return ValueAddedBatchImportOperationType.DEDUCTION;
    }



    @Override
    public void validateOrderStatus(ValueAddedDeliveryOrder order) {
        String currentStatus = order.getStatus();
        if (!ValueAddedDeliveryOrderStatus.CONFIRMED_PENDING_DEDUCTION.getCode().equals(currentStatus)) {
            throw new IllegalArgumentException(
                    String.format("Delivery order %s status does not allow deduction operation, current status: %s, required status: %s",
                            order.getDeliveryOrderNo(),
                            ValueAddedDeliveryOrderStatus.getByCode(currentStatus).getDescription(),
                            ValueAddedDeliveryOrderStatus.CONFIRMED_PENDING_DEDUCTION.getDescription())
            );
        }
    }

    @Override
    public String getTargetStatus(String currentStatus) {
        // 扣款操作：已确认待扣款 -> 已扣款
        if (ValueAddedDeliveryOrderStatus.CONFIRMED_PENDING_DEDUCTION.getCode().equals(currentStatus)) {
            return ValueAddedDeliveryOrderStatus.DEDUCTION_COMPLETED.getCode();
        }
        return null;
    }



    @Override
    public int processFileSaving(
            ValueAddedDeliveryOrder order,
            Map<String, String> extractedFiles,
            BatchImportOperationRequest request) {

        int savedCount = 0;
        String deliveryOrderNo = order.getDeliveryOrderNo();

        for (Map.Entry<String, String> entry : extractedFiles.entrySet()) {
            try {
                String fileName = entry.getKey();
                String filePath = entry.getValue();

                // Check if file name is related to delivery order number
                if (isFileRelatedToOrder(fileName, deliveryOrderNo)) {
                    ValueAddedFile file = new ValueAddedFile();
                    file.setDeliveryOrderNo(deliveryOrderNo);
                    file.setFileName(fileName);
                    file.setFileUrl(filePath);
                    file.setFileType(1); // 1-交付材料附件（扣款凭证）
                    file.setStatus(1); // 1-处理完成
                    file.setIsDel(false);
                    file.setRemark("批量扣款导入凭证");
                    file.setCreateBy(SecurityUtils.getUserId().toString());

                    boolean saved = fileService.save(file);
                    if (saved) {
                        savedCount++;
                        log.debug("Deduction voucher file saved successfully: {} -> {}", fileName, deliveryOrderNo);
                    }
                }
            } catch (Exception e) {
                log.warn("Deduction voucher file save failed: {}, error: {}", entry.getKey(), e.getMessage());
            }
        }

        return savedCount;
    }

    /**
     * 判断文件是否与交付单相关
     * 扣款操作的文件通常是扣款凭证或相关证明文件
     */
    private boolean isFileRelatedToOrder(String fileName, String deliveryOrderNo) {
        if (fileName == null || deliveryOrderNo == null) {
            return false;
        }

        String upperFileName = fileName.toUpperCase();
        String upperOrderNo = deliveryOrderNo.toUpperCase();

        // Deduction file matching rules:
        // 1. File name contains delivery order number
        // 2. Or file name contains keywords like "deduction", "voucher"
        return upperFileName.contains(upperOrderNo) ||
               upperFileName.contains("DEDUCTION") ||
               upperFileName.contains("VOUCHER") ||
               upperFileName.contains("PAYMENT") ||
               upperFileName.contains("RECEIPT");
    }

    @Override
    public TemplateParseResult parseTemplateFile(MultipartFile templateFile) throws Exception {
        log.info("开始解析扣款操作Excel模板文件: {}", templateFile.getOriginalFilename());

        try {
            // 使用ExcelUtil解析Excel文件为DeductionImportExcelDTO列表
            ExcelUtil<DeductionImportExcelDTO> excelUtil = new ExcelUtil<>(DeductionImportExcelDTO.class);
            List<DeductionImportExcelDTO> dataList = excelUtil.importExcel(templateFile.getInputStream());

            // 设置行号用于错误定位
            for (int i = 0; i < dataList.size(); i++) {
                dataList.get(i).setRowNumber(i + 2); // Excel从第2行开始是数据行
            }

            // 使用新的基础校验逻辑
            ImportValidationResult basicValidationResult = valueAddedImportValidationHelper.performBasicValidation(dataList);

            if (!basicValidationResult.getIsValid()) {
                // 基础校验失败，返回错误结果
                return TemplateParseResult.failure(basicValidationResult.getErrors());
            }

            // 基础校验通过，扣款操作暂时不需要特殊校验，直接返回成功结果
            return TemplateParseResult.success(basicValidationResult.getValidData());

        } catch (Exception e) {
            log.error("扣款操作Excel模板文件解析失败: {}", e.getMessage(), e);
            throw new Exception("扣款操作Excel模板文件解析失败: " + e.getMessage(), e);
        }
    }
}
